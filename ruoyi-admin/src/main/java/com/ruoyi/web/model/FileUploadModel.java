package com.ruoyi.web.model;

import org.springframework.web.multipart.MultipartFile;

public class FileUploadModel {

    public static final Integer OCR_TYPE_ID_CARD = 1;

    public static final Integer OCR_TYPE_VEHICLE_LICENSE = 2;

    public static final Integer OCR_TYPE_BUSINESS_LICENSE = 3;

    public static final Integer OCR_TYPE_CAR_INVOICE = 4;

    private MultipartFile file;
    private String relatedTable;
    private Integer ocrType;

    public MultipartFile getFile() {
        return file;
    }

    public void setFile(MultipartFile file) {
        this.file = file;
    }

    public String getRelatedTable() {
        return relatedTable;
    }

    public void setRelatedTable(String relatedTable) {
        this.relatedTable = relatedTable;
    }

    public Integer getOcrType() {
        return ocrType;
    }

    public void setOcrType(Integer ocrType) {
        this.ocrType = ocrType;
    }

    public static String convertDir(String relatedTable) {
        if ("p_product_order".equals(relatedTable)) {
            return "order";
        } else if ("p_product_order_insurance".equals(relatedTable)) {
            return "insurance";
        }
        return "public";
    }
}
