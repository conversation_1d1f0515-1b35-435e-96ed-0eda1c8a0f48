package com.ruoyi.web.controller.yanbao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.OssUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysFile;
import com.ruoyi.system.service.ISysFileService;
import com.ruoyi.web.controller.yanbao.process.ProductOrderProcesss;
import com.ruoyi.yanbao.entity.CarBrand;
import com.ruoyi.yanbao.entity.Product;
import com.ruoyi.yanbao.entity.ProductOrder;
import com.ruoyi.yanbao.entity.ProductTerm;
import com.ruoyi.yanbao.entity.Store;
import com.ruoyi.yanbao.entity.vo.ProductOrderVo;
import com.ruoyi.yanbao.service.CarBrandService;
import com.ruoyi.yanbao.service.ProductOrderService;
import com.ruoyi.yanbao.service.ProductService;
import com.ruoyi.yanbao.service.ProductTermService;
import com.ruoyi.yanbao.service.StoreService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/yanbao/order")
public class ProductOrderController extends BaseController {

    @Autowired
    private ProductOrderService productOrderService;

    @Autowired
    private ProductService productService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private ProductTermService productTermService;

    @Autowired
    private ISysFileService sysFileService;

    @Autowired
    private CarBrandService carBrandService;

    @Autowired
    private ProductOrderProcesss productOrderProcesss;

    @PreAuthorize("@ss.hasPermi('yanbao:order:add')")
    @Log(title = "新增订单", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResult add(@RequestBody ProductOrderVo order) {
        ProductOrder po = (ProductOrder) order;
        Product product = productService.getById(order.getProductId());
        if (product == null || !Constants.STATUS_ENABLE.equals(product.getStatus())) {
            return AjaxResult.error("产品已失效");
        }
        po.setProductName(product.getName());
        po.setProductType(product.getType());
        po.setSalesMode(product.getSalesMode());
        if (po.getStoreId() != null) {
            Store store = storeService.getById(po.getStoreId());
            po.setStoreName(store.getName());
        }
        if (po.getProductTermId() != null) {
            ProductTerm productTerm = productTermService.getById(po.getProductTermId());
            po.setProductTermName(productTerm.getName());
            po.setServicePeriod(productTerm.getTimeLimit().intValue());
            if (po.getServiceEnableDate() != null) {
                po.setServiceDisableDate(DateUtils.addDays(DateUtils.addMonths(po.getServiceEnableDate(), po.getServicePeriod()), -1));
            }
        }
        List<Long> sysFileIds = productOrderProcesss.processOrderFile(po, order);
        productOrderProcesss.matchSuggestPrice(po, product);

        if (po.getId() == null) {
            po.setSaleUserId(getUserId());
            po.setSaleUserName(getLoginUser().getUser().getNickName());
            po.setCreatedBy(getLoginUser().getUser().getNickName());
            po.setOrderNo(System.currentTimeMillis() + "");
            return toAjax(productOrderService.saveOrder(po, sysFileIds));
        } else {
            po.setChangedBy(getUsername());
            ProductOrder old = productOrderService.getById(order.getId());
            //暂存和驳回状态的可以修改
            if (!(ProductOrderVo.OrderState.STAGING.equals(old.getOrderState()) || ProductOrderVo.OrderState.REJECT.equals(old.getOrderState()))) {
                return AjaxResult.error("只有暂存或驳回状态的订单才能修改");
            }
            po.setSaleUserId(old.getSaleUserId());
            po.setSaleUserName(old.getSaleUserName());
            po.setCreatedAt(old.getCreatedAt());
            po.setCreatedBy(old.getCreatedBy());
            return toAjax(productOrderService.saveOrder(po, sysFileIds));
        }
    }

    /**
     * 查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('yanbao:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductOrderVo productOrder) {
        startPage();
        LambdaQueryWrapper<ProductOrder> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(productOrder.getCustomerName())) {
            queryWrapper.like(ProductOrder::getCustomerName, productOrder.getCustomerName());
        }
        if (StringUtils.isNotBlank(productOrder.getContactTelephone())) {
            queryWrapper.like(ProductOrder::getContactTelephone, productOrder.getContactTelephone());
        }
        if (StringUtils.isNotBlank(productOrder.getVinNo())) {
            queryWrapper.like(ProductOrder::getVinNo, productOrder.getVinNo());
        }
        if (StringUtils.isNotBlank(productOrder.getCarNo())) {
            queryWrapper.like(ProductOrder::getCarNo, productOrder.getCarNo());
        }
        if (productOrder.getOrderState() != null) {
            queryWrapper.eq(ProductOrder::getOrderState, productOrder.getOrderState());
        }
        if (productOrder.getSaleUserId() != null) {
            queryWrapper.eq(ProductOrder::getSaleUserId, productOrder.getSaleUserId());
        }
        if (productOrder.getStoreId() != null) {
            queryWrapper.eq(ProductOrder::getStoreId, productOrder.getStoreId());
        }
        if (productOrder.getProductId() != null) {
            queryWrapper.eq(ProductOrder::getProductId, productOrder.getProductId());
        }
        if (productOrder.getCheckoutState() != null) {
            queryWrapper.eq(ProductOrder::getCheckoutState, productOrder.getCheckoutState());
        }
        LoginUser loginUser = getLoginUser();
        //角色包含销售，则只能查看自己的订单
        Set<String> roleKeys = new HashSet<>();
        if (getLoginUser().getUser().getRoles() != null) {
            for (SysRole sysRole : getLoginUser().getUser().getRoles()) {
                roleKeys.add(sysRole.getRoleKey());
            }
        }
        if (roleKeys.contains("sales")) {
            queryWrapper.eq(ProductOrder::getSaleUserId, getUserId());
        }
        queryWrapper.orderByDesc(ProductOrder::getCreatedAt);

        List<ProductOrder> list = productOrderService.list(queryWrapper);
        return getDataTable(list);
    }

    /**
     * 获取订单详情
     */
    @PreAuthorize("@ss.hasPermi('yanbao:order:detail')")
    @GetMapping("/detail")
    public AjaxResult getOrderDetail(Long orderId) {
        ProductOrder order = productOrderService.getById(orderId);
        if (order == null) {
            return AjaxResult.error("订单不存在");
        }
        ProductOrderVo orderDetail = new ProductOrderVo();
        BeanUtils.copyProperties(order, orderDetail);
        if (order.getCarBrandId() != null) {
            CarBrand carBrand = carBrandService.getById(order.getCarBrandId());
            if (carBrand != null) {
                orderDetail.setCarBrandName(carBrand.getName());
            }
        }
        // 设置图片列表
        setImageLists(orderDetail, order);
        return AjaxResult.success(orderDetail);
    }

    /**
     * 设置订单的图片列表
     */
    private void setImageLists(ProductOrderVo orderDetail, ProductOrder order) {
        // 证件图片
        if (order.getCertificateImg() != null) {
            orderDetail.setCertificateImgList(getFileItemList(order.getCertificateImg()));
        }

        // 行驶证图片
        if (order.getVehicleLicenseImg() != null) {
            orderDetail.setVehicleLicenseImgList(getFileItemList(order.getVehicleLicenseImg()));
        }

        // 购车发票图片
        if (order.getCarInvoiceImg() != null) {
            orderDetail.setCarInvoiceImgList(getFileItemList(order.getCarInvoiceImg()));
        }
        List<OssUtil.FileItem> carFileList = new ArrayList<>();
        // 左前45°图片
        if (order.getLeftImg() != null) {
            orderDetail.setLeftImgList(getFileItemList(order.getLeftImg()));
            carFileList.addAll(orderDetail.getLeftImgList());
        }

        // 右前45°图片
        if (order.getRightImg() != null) {
            orderDetail.setRightImgList(getFileItemList(order.getRightImg()));
            carFileList.addAll(orderDetail.getRightImgList());
        }

        // 左后45°图片
        if (order.getLeftbackImg() != null) {
            orderDetail.setLeftbackImgList(getFileItemList(order.getLeftbackImg()));
            carFileList.addAll(orderDetail.getLeftbackImgList());
        }

        // 右后45°图片
        if (order.getRightbackImg() != null) {
            orderDetail.setRightbackImgList(getFileItemList(order.getRightbackImg()));
            carFileList.addAll(orderDetail.getRightbackImgList());
        }

        // VIN码图片
        if (order.getVinImg() != null) {
            orderDetail.setVinImgList(getFileItemList(order.getVinImg()));
            carFileList.addAll(orderDetail.getVinImgList());
        }
        orderDetail.setCarImgList(carFileList);

        // 行驶里程图片
        if (order.getCarMileageImg() != null) {
            orderDetail.setCarMileageImgList(getFileItemList(order.getCarMileageImg()));
        }

        // 完税证明图片
        if (order.getPurchaseTaxCompleteImg() != null) {
            orderDetail.setPurchaseTaxCompleteImgList(getFileItemList(order.getPurchaseTaxCompleteImg()));
        }

        // 上牌费用发票图片
        if (order.getVehicleLicenseInvoiceImg() != null) {
            orderDetail.setVehicleLicenseInvoiceImgList(getFileItemList(order.getVehicleLicenseInvoiceImg()));
        }

        // 交强险保单图片
        if (order.getTrafficInsuranceImg() != null) {
            orderDetail.setTrafficInsuranceImgList(getFileItemList(order.getTrafficInsuranceImg()));
        }

        // 商业险保单图片
        if (order.getCommercialInsuranceImg() != null) {
            orderDetail.setCommercialInsuranceImgList(getFileItemList(order.getCommercialInsuranceImg()));
        }

        // 服务合同图片
        if (order.getServiceContractImg() != null) {
            orderDetail.setServiceContractImgList(getFileItemList(order.getServiceContractImg()));
        }

        // 付款小票图片
        if (order.getPayImg() != null) {
            orderDetail.setPayImgList(getFileItemList(order.getPayImg()));
        }
    }

    /**
     * 根据文件ID获取文件项列表
     */
    private List<OssUtil.FileItem> getFileItemList(String fileId) {
        List<OssUtil.FileItem> fileItems = new ArrayList<>();
        if (StringUtils.isNotBlank(fileId)) {
            List<SysFile> sysFiles = sysFileService.selectSysFileByIds(StringUtils.stringToLong(fileId));
            if (CollectionUtils.isNotEmpty(sysFiles)) {
                for (SysFile sysFile : sysFiles) {
                    OssUtil.FileItem fileItem = new OssUtil.FileItem();
                    fileItem.setId(sysFile.getId());
                    fileItem.setName(sysFile.getName());
                    fileItem.setPath(sysFile.getPath());
                    fileItem.setSize(sysFile.getSize());
                    // 根据路径生成访问URL
                    fileItem.setUrl(generateFileUrl(sysFile));
                    fileItems.add(fileItem);
                }
            }
        }
        return fileItems;
    }

    /**
     * 根据文件路径生成访问URL
     */
    private String generateFileUrl(SysFile file) {
        String path = file.getPath();
        if (StringUtils.isEmpty(path)) {
            return "";
        }
        // 如果是OSS路径，直接返回完整URL
        if (path.startsWith("http://") || path.startsWith("https://")) {
            return path;
        }
        // 否则生成OSS访问URL
        //大于1M的图片压缩
        if (file.getSize() >= 1024 * 1024) {
            return OssUtil.getObjectUrl(path, null, "image/quality,q_80");
        } else {
            return OssUtil.getObjectUrl(path, null, null);
        }
    }

    @PreAuthorize("@ss.hasPermi('yanbao:order:audit')")
    @Log(title = "订单审核", businessType = BusinessType.UPDATE)
    @PostMapping("/audit")
    public AjaxResult audit(@RequestBody ProductOrderVo param) {
        return toAjax(productOrderService.audit(param));

    }

    @PreAuthorize("@ss.hasPermi('yanbao:order:checkout')")
    @Log(title = "订单确认收款", businessType = BusinessType.UPDATE)
    @PostMapping("/checkout")
    public AjaxResult checkout(@RequestBody ProductOrderVo param) {
        return toAjax(productOrderService.checkout(param));

    }

    @PreAuthorize("@ss.hasPermi('yanbao:order:delete')")
    @Log(title = "订单删除", businessType = BusinessType.UPDATE)
    @PostMapping("/delete")
    public AjaxResult delete(@RequestBody ProductOrderVo param) {
        ProductOrder order = productOrderService.getById(param.getId());
        if (order == null) {
            return AjaxResult.error("订单不存在");
        }
        if (ProductOrderVo.OrderState.STAGING.equals(order.getOrderState()) || ProductOrderVo.OrderState.REJECT.equals(order.getOrderState())) {
            order.setChangedBy(getUsername());
            return toAjax(productOrderService.removeById(order));
        } else {
            return AjaxResult.error("只有暂存或驳回状态的订单才能删除");
        }
    }

    @GetMapping("/getAllSaleUser")
    public AjaxResult getAllSaleUser() {
        return AjaxResult.success(productOrderService.getAllSaleUser());
    }

    @GetMapping("/getAllProduct")
    public AjaxResult getAllProduct() {
        return AjaxResult.success(productOrderService.getAllProduct());
    }
}
