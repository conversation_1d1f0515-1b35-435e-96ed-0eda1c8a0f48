package com.ruoyi.yanbao;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.fill.Column;

import java.nio.file.Paths;
import java.sql.Types;
import java.util.HashMap;
import java.util.Map;

public class CodeGenerator {
    public static void main(String[] args) {
        Map<OutputFile, String> pathInfo = new HashMap<>();
        pathInfo.put(OutputFile.xml, Paths.get(System.getProperty("user.dir")) + "/ruoyi-yanbao/src/main/resources/mapper");

        FastAutoGenerator.create("***********************************************************", "root", "Carme501!")
                .globalConfig(builder -> {
                    builder.author("jiangping") // 设置作者
                            .outputDir(Paths.get(System.getProperty("user.dir")) + "/ruoyi-yanbao/src/main/java"); // 指定输出目录
                })
                .dataSourceConfig(builder ->
                        builder.typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                            int typeCode = metaInfo.getJdbcType().TYPE_CODE;
                            if (typeCode == Types.SMALLINT) {
                                // 自定义类型转换
                                return DbColumnType.INTEGER;
                            }
                            if (typeCode == Types.TINYINT) {
                                // 自定义类型转换
                                return DbColumnType.INTEGER;
                            }
                            if (typeCode == Types.DATE || typeCode == Types.TIME || typeCode == Types.TIMESTAMP) {
                                return DbColumnType.DATE;
                            }
                            return typeRegistry.getColumnType(metaInfo);
                        })
                )
                .packageConfig(builder ->
                        builder.parent("com.ruoyi.yanbao") // 设置父包名
                                .pathInfo(pathInfo) // 设置mapperXml生成路径
                )
                .strategyConfig(builder ->
                        builder.addInclude("p_insurance_company")
                                .addTablePrefix("p_") // 去除表名前缀p_
                                .entityBuilder().logicDeleteColumnName("is_delete")
                                .addTableFills(new Column("is_delete", FieldFill.INSERT),
                                        new Column("created_at", FieldFill.INSERT),
                                        new Column("changed_at", FieldFill.UPDATE)).enableFileOverride()
                                .mapperBuilder()
                                .serviceBuilder().formatServiceFileName("%sService")
                                .controllerBuilder().disable()// 设置需要生成的表名
                                .mapperBuilder()
                ).templateEngine(new FreemarkerTemplateEngine())// 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();
    }
}
