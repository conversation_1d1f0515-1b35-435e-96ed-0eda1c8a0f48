package com.ruoyi.common.utils;

import com.alibaba.fastjson2.JSONObject;
import com.aliyun.ocr_api20210707.Client;
import com.aliyun.ocr_api20210707.models.RecognizeBusinessLicenseRequest;
import com.aliyun.ocr_api20210707.models.RecognizeBusinessLicenseResponse;
import com.aliyun.ocr_api20210707.models.RecognizeCarInvoiceRequest;
import com.aliyun.ocr_api20210707.models.RecognizeCarInvoiceResponse;
import com.aliyun.ocr_api20210707.models.RecognizeIdcardRequest;
import com.aliyun.ocr_api20210707.models.RecognizeIdcardResponse;
import com.aliyun.ocr_api20210707.models.RecognizeVehicleLicenseRequest;
import com.aliyun.ocr_api20210707.models.RecognizeVehicleLicenseResponse;
import com.aliyun.teautil.models.RuntimeOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;

public class AliyunOcrUtil {


    private static final Logger log = LoggerFactory.getLogger(AliyunOcrUtil.class);

    private static String accessKeyId;
    private static String accessKeySecret;
    private static String endpoint;
    private static Client aliyunOcrClient;

    public static String getAccessKeyId() {
        return accessKeyId;
    }

    public static void setAccessKeyId(String accessKeyId) {
        AliyunOcrUtil.accessKeyId = accessKeyId;
    }

    public static String getAccessKeySecret() {
        return accessKeySecret;
    }

    public static void setAccessKeySecret(String accessKeySecret) {
        AliyunOcrUtil.accessKeySecret = accessKeySecret;
    }

    public static String getEndpoint() {
        return endpoint;
    }

    public static void setEndpoint(String endpoint) {
        AliyunOcrUtil.endpoint = endpoint;
    }

    public static Client getAliyunOcrClient() {
        return aliyunOcrClient;
    }

    public static void setAliyunOcrClient(Client aliyunOcrClient) {
        AliyunOcrUtil.aliyunOcrClient = aliyunOcrClient;
    }

    private synchronized static Client getClient() {
        try {
            if (aliyunOcrClient == null && StringUtils.isNotBlank(endpoint)) {
                com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                        // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                        .setAccessKeyId(accessKeyId)
                        // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
                        .setAccessKeySecret(accessKeySecret);
                // 默认公网接入地址为"ocr-api.cn-hangzhou.aliyuncs.com"，若您需要使用vpc域名访问，请确保您的ecs建立在杭州region，vpc接入地址为"ocr-api-vpc.cn-hangzhou.aliyuncs.com"
                config.endpoint = endpoint;
                aliyunOcrClient = new Client(config);
            }
        } catch (Exception e) {
            log.error("initOcrFail", e);
        }

        return aliyunOcrClient;
    }

    /**
     * 身份证识别
     *
     * @param body 图片二进制
     * @param url  图片地址
     */
    public static JSONObject recognizeIdcard(InputStream body, String url) {
        try {
            Client client = getClient();
            if (client != null && (body != null || StringUtils.isNotBlank(url))) {
                RecognizeIdcardRequest request = new RecognizeIdcardRequest();
                if (body != null) {
                    request.setBody(body);
                } else {
                    request.setUrl(url);
                }
                RuntimeOptions runtimeOptions = new RuntimeOptions();
                RecognizeIdcardResponse response = client.recognizeIdcardWithOptions(request, runtimeOptions);
                if (response != null && response.getStatusCode() == 200 &&
                        response.getBody().getCode() == null && response.getBody() != null) {
                    String data = response.getBody().getData();
                    JSONObject jsonObject = JSONObject.parseObject(data);
                    if (jsonObject.getJSONObject("data") != null) {
                        return jsonObject.getJSONObject("data");
                    }
                }
            }
        } catch (Exception e) {
            log.error("身份证识别失败：", e);
        }
        return null;
    }

    /**
     * 行驶证识别
     *
     * @param body 图片二进制
     * @param url  图片地址
     */
    public static JSONObject recognizeVehicleLicense(InputStream body, String url) {
        try {
            Client client = getClient();
            if (client != null && (body != null || StringUtils.isNotBlank(url))) {
                RecognizeVehicleLicenseRequest request = new RecognizeVehicleLicenseRequest();
                if (body != null) {
                    request.setBody(body);
                } else {
                    request.setUrl(url);
                }
                RuntimeOptions runtimeOptions = new RuntimeOptions();
                RecognizeVehicleLicenseResponse response = client.recognizeVehicleLicenseWithOptions(request, runtimeOptions);
                if (response != null && response.getStatusCode() == 200 &&
                        response.getBody().getCode() == null && response.getBody() != null) {
                    String data = response.getBody().getData();
                    JSONObject jsonObject = JSONObject.parseObject(data);
                    if (jsonObject.getJSONObject("data") != null) {
                        return jsonObject.getJSONObject("data");
                    }
                }
            }
        } catch (Exception e) {
            log.error("行驶证识别失败：", e);
        }
        return null;
    }

    public static JSONObject recognizeBusinessLicense(InputStream body, String url) {
        try {
            Client client = getClient();
            if (client != null && (body != null || StringUtils.isNotBlank(url))) {
                RecognizeBusinessLicenseRequest request = new RecognizeBusinessLicenseRequest();
                if (body != null) {
                    request.setBody(body);
                } else {
                    request.setUrl(url);
                }
                RecognizeBusinessLicenseResponse response = client.recognizeBusinessLicense(request);
                if (response != null && response.getStatusCode() == 200 &&
                        response.getBody().getCode() == null && response.getBody() != null) {
                    String data = response.getBody().getData();
                    JSONObject jsonObject = JSONObject.parseObject(data);
                    if (jsonObject.getJSONObject("data") != null) {
                        return jsonObject.getJSONObject("data");
                    }
                }
            }
        } catch (Exception e) {
            log.error("营业执照识别失败：", e);
        }
        return null;
    }

    public static JSONObject recognizeCarInvoice(InputStream body, String url) {
        try {
            Client client = getClient();
            if (client != null && (body != null || StringUtils.isNotBlank(url))) {
                RecognizeCarInvoiceRequest request = new RecognizeCarInvoiceRequest();
                if (body != null) {
                    request.setBody(body);
                } else {
                    request.setUrl(url);
                }
                RecognizeCarInvoiceResponse response = client.recognizeCarInvoice(request);
                if (response != null && response.getStatusCode() == 200 &&
                        response.getBody().getCode() == null && response.getBody() != null) {
                    String data = response.getBody().getData();
                    JSONObject jsonObject = JSONObject.parseObject(data);
                    if (jsonObject.getJSONObject("data") != null) {
                        return jsonObject.getJSONObject("data");
                    }
                }
            }
        } catch (Exception e) {
            log.error("机动车发票识别失败：", e);
        }
        return null;
    }


}
