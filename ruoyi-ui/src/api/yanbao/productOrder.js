import request from '@/utils/request'

// 查询订单列表
export function listOrder(query) {
  return request({
    url: '/yanbao/order/list',
    method: 'get',
    params: query
  })
}

// 提交订单
export function addOrder(data) {
  return request({
    url: '/yanbao/order/add',
    method: 'post',
    data: data
  })
}

// 获取订单详情
export function getOrder(query) {
  return request({
    url: '/yanbao/order/detail',
    method: 'get',
    params: query
  })
}

// 审核订单
export function auditOrder(data) {
  return request({
    url: '/yanbao/order/audit',
    method: 'post',
    data: data
  })
}
// 删除订单
export function deleteOrder(data) {
  return request({
    url: '/yanbao/order/delete',
    method: 'post',
    data: data
  })
}

export function getAllSaleUser(query) {
  return request({
    url: '/yanbao/order/getAllSaleUser',
    method: 'get',
    params: query
  })
}

export function getAllProduct(query) {
  return request({
    url: '/yanbao/order/getAllProduct',
    method: 'get',
    params: query
  })
}

// 确认收款
export function checkoutOrder(data) {
  return request({
    url: '/yanbao/order/checkout',
    method: 'post',
    data: data
  })
}

