<template>
  <div class="app-container home">

    <el-card style="margin-bottom: 10px;" v-hasPermi="['yanbao:order:add']" v-for="product in products" :key="product.id" footer-class="footer">
      <template #header>
        <div class="clearfix">
          <span>{{ product.name }}</span>
          <span style="float: right"><router-link :to="'/yanbao/order/add?productId=' + product.id"><el-button size="small" type="primary">录单</el-button></router-link></span>
        </div>
      </template>
      <div class="body">
        <el-text line-clamp="3" size="small">
          {{ product.remark }}
        </el-text>
      </div>
    </el-card>
    <el-tabs model-value="activeName" @tab-click="tabClick">
      <el-tab-pane label="待审核" name="wait">
        <el-table v-loading="loading" :data="orderList" size="small">
          <el-table-column label="订单" fixed align="center" prop="id" min-width="300px">
            <template #default="scope">
              <el-descriptions>
                <el-descriptions-item label="订单号">{{ scope.row.id }}</el-descriptions-item>
              </el-descriptions>
              <el-descriptions>
                <el-descriptions-item label="产品">{{ scope.row.productName }}</el-descriptions-item>
              </el-descriptions>
              <el-descriptions>
                <el-descriptions-item label="车牌号">{{ scope.row.carNo }}</el-descriptions-item>
              </el-descriptions>
              <el-descriptions>
                <el-descriptions-item label="车架号">{{ scope.row.vinNo }}</el-descriptions-item>
              </el-descriptions>
              <el-descriptions>
                <el-descriptions-item label="车主">{{ scope.row.customerName }}</el-descriptions-item>
              </el-descriptions>
              <el-descriptions>
                <el-descriptions-item label="联系电话">{{ scope.row.contactTelephone }}</el-descriptions-item>
              </el-descriptions>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button v-if="scope.row.orderState==1" link type="primary" @click="handleDetail(scope.row)" v-hasPermi="['yanbao:order:audit']">审核</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
            v-show="total>0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="待确认收款" name="confirm" @tab-click="tabClick">

      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup name="Index">
  import { getAvailableProducts } from "@/api/yanbao/product"
  import {parseTime} from "@/utils/ruoyi.js";
  import {listOrder} from "@/api/yanbao/productOrder.js";

  const products = ref([]);
  const orderList = ref([]);
  const total = ref(0);
  const loading = ref(false);
  const activeName = ref(undefined);

  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10,
    }
  })

  const { queryParams } = toRefs(data)

  function tabClick(tab) {
    console.log(tab)
    activeName.value = tab.props.name
    queryParams.value.pageNum = 1
    queryParams.value.pageSize = 10
    if (activeName.value=='wait'){
      queryParams.value.orderState = 1
    }else{
      queryParams.value.orderState = 3
    }
    getList()
  }

  function getList() {
    loading.value = true
    listOrder(queryParams.value).then(response => {
      orderList.value = response.rows
      total.value = response.total
      loading.value = false
    })
  }

  function handleDetail(row) {
    router.push({
      path: "/yanbao/order/detail",
      query: { orderId: row.id }
    })
  }


  getAvailableProducts().then(resp=>{
    products.value = resp.data
  })
</script>

<style scoped lang="scss">
.footer {
  display: flex;
  justify-content: space-between;
}

.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }
  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }
  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }
}
</style>

